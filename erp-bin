#!/usr/bin/env python3
"""
ERP System - Database Management Tool
CLI tool for database initialization and server startup
"""
import sys
import os
import argparse
import asyncio
from pathlib import Path

# Add current directory to Python path
erp_root = Path(__file__).parent
sys.path.insert(0, str(erp_root))

# Import ERP components
from erp.config import config
from erp.logging import initialize_logging, get_logger


async def is_database_initialized(db_name: str) -> bool:
    """
    Check if a database is already initialized with ERP system
    by checking if base module is installed in ir.module.module table
    """
    try:
        from erp.database.memory.registry_manager import MemoryRegistryManager
        return await MemoryRegistryManager._is_base_module_installed(db_name)
    except Exception as e:
        # Use logger for consistency, but fallback to print if logging not available
        try:
            logger = get_logger(__name__)
            logger.warning(f"⚠️ Error checking database initialization status: {e}")
        except:
            print(f"⚠️ Error checking database initialization status: {e}")
        return False


async def initialize_database(db_name: str, force: bool = False, demo: bool = False) -> bool:
    """Initialize database with ERP system"""
    logger = get_logger(__name__)
    try:
        from erp.database.registry.database_registry import DatabaseRegistry
        from erp.database.registry.lifecycle import DatabaseLifecycle
        from erp.database.registry.initialization import DatabaseInitializer

        logger.info(f"🚀 Initializing database: {db_name}")

        # Check if database is already initialized
        if await is_database_initialized(db_name):
            if not force:
                logger.info(f"✅ Database '{db_name}' is already initialized with ERP system")
                logger.info("   Skipping initialization (use --force to reinitialize)")
                return True
            else:
                logger.warning(f"⚠️ Database '{db_name}' is already initialized, but --force flag specified")
                logger.warning("   Proceeding with reinitialization...")

        # Check if database exists first
        existing_dbs = await DatabaseRegistry.list_databases()
        db_exists = db_name in existing_dbs

        # Create database if it doesn't exist
        if not db_exists:
            logger.info(f"📊 Creating database: {db_name}")
            created = await DatabaseLifecycle.create_database(db_name)
            if not created:
                logger.error(f"❌ Failed to create database: {db_name}")
                return False
            logger.info(f"✅ Database '{db_name}' created successfully")

        # Initialize the database
        logger.debug("🔧 Installing base modules and setting up initial data...")
        success = await DatabaseInitializer.initialize_database(db_name)

        if success:
            logger.info(f"✅ Database '{db_name}' initialized successfully!")

            # Print database info
            from erp.utils.system_info import get_database_info, print_database_info
            try:
                db_info = await get_database_info(db_name)
                print_database_info(db_info)
            except Exception as e:
                logger.debug(f"Could not get database info: {e}")

            # Set the initialized database as the current database in config
            config.set('options', 'db_name', db_name)
            return True
        else:
            logger.error(f"❌ Failed to initialize database: {db_name}")
            return False

    except Exception as e:
        logger.error(f"💥 Failed to initialize database: {e}")
        return False


async def start_http_server(db_name: str, host: str = None, port: int = None, reload: bool = False):
    """Start the FastAPI HTTP server with the initialized database"""
    logger = get_logger(__name__)
    import uvicorn
    from erp.server import create_app
    try:
        logger.info(f"🌐 Starting HTTP server with database '{db_name}'...")

        # Get server configuration from config or use provided values
        server_host = host or config.get('options', 'http_interface', fallback='127.0.0.1')
        server_port = port or config.getint('options', 'http_port', fallback=8069)

        logger.info(f"🚀 Server starting on http://{server_host}:{server_port}")
        logger.info(f"📊 Database: {db_name}")
        if reload:
            logger.info("🔄 Auto-reload enabled - watching Python files for changes")
        logger.info("✅ Ready - Press Ctrl+C to stop")

        # Set the database as the current database in config
        config.set('options', 'db_name', db_name)

        # Create the FastAPI app
        app = create_app()

        # Configure uvicorn
        uvicorn_config = uvicorn.Config(
            app=app,
            host=server_host,
            port=server_port,
            log_level="info",
            access_log=True,
            reload=reload,
            reload_includes=["*.py"] if reload else None,
            reload_excludes=["*.pyc", "__pycache__/*", "*.log", "*.tmp"] if reload else None
        )

        # Start the server
        server = uvicorn.Server(uvicorn_config)
        await server.serve()

    except Exception as e:
        logger.error(f"❌ Failed to start HTTP server: {e}")
        return False


async def start_server_without_http(db_name: str):
    """Keep the process running with the initialized database without HTTP server"""
    logger = get_logger(__name__)
    logger.info("🚫 HTTP server startup skipped (--no-http flag specified)")
    logger.info(f"📊 Database '{db_name}' is ready and selected as current database")
    logger.info("💡 You can start the HTTP server later with: erp-bin start")
    logger.info("🔄 Keeping process running with initialized database...")
    logger.info("   Press Ctrl+C to exit")

    try:
        # Keep the process running
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("\n🛑 Process terminated by user")


async def start_existing_database(db_name: str, host: str = None, port: int = None, reload: bool = False):
    """Start the HTTP server with an existing database"""
    logger = get_logger(__name__)

    try:
        # Check if database exists and is initialized
        if not await is_database_initialized(db_name):
            logger.error(f"❌ Database '{db_name}' is not initialized with ERP system")
            logger.error("   Please run 'erp-bin init {db_name}' first to initialize the database")
            return False

        logger.info(f"✅ Database '{db_name}' is initialized and ready")

        # Start the HTTP server
        await start_http_server(db_name, host, port, reload)
        return True

    except Exception as e:
        logger.error(f"❌ Failed to start server with database '{db_name}': {e}")
        return False


async def start_server_without_database(host: str = None, port: int = None, reload: bool = False):
    """Start the HTTP server without any database context"""
    logger = get_logger(__name__)
    import uvicorn
    from erp.server import create_app
    try:
        logger.info("🌐 Starting HTTP server without database context...")

        # Get server configuration from config or use provided values
        server_host = host or config.get('options', 'http_interface', fallback='127.0.0.1')
        server_port = port or config.getint('options', 'http_port', fallback=8069)

        logger.info(f"🚀 Server starting on http://{server_host}:{server_port}")
        logger.info("💡 Multi-database mode - no default database")
        if reload:
            logger.info("🔄 Auto-reload enabled - watching Python files for changes")
        logger.info("✅ Ready - Press Ctrl+C to stop")

        # Don't set any database in config when starting without context
        # This allows the server to run in multi-database mode

        # Create the FastAPI app
        app = create_app()

        # Configure uvicorn
        uvicorn_config = uvicorn.Config(
            app=app,
            host=server_host,
            port=server_port,
            log_level="info",
            access_log=True,
            reload=reload,
            reload_includes=["*.py"] if reload else None,
            reload_excludes=["*.pyc", "__pycache__/*", "*.log", "*.tmp"] if reload else None
        )

        # Start the server
        server = uvicorn.Server(uvicorn_config)
        await server.serve()

    except Exception as e:
        logger.error(f"❌ Failed to start HTTP server: {e}")
        return False


def create_parser():
    """Create argument parser for init and start commands"""
    parser = argparse.ArgumentParser(
        description='ERP System Database Management Tool',
        prog='erp-bin',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  erp-bin init mydb                    # Initialize database and keep running
  erp-bin init mydb --exit             # Initialize database and exit
  erp-bin init mydb --no-http          # Initialize database without HTTP server
  erp-bin init mydb --force            # Force reinitialize existing database

  erp-bin start                        # Start HTTP server without database context
  erp-bin start --db mydb              # Start HTTP server with specific database
  erp-bin start --host 0.0.0.0         # Start server on all interfaces
  erp-bin start --port 8080            # Start server on custom port
  erp-bin start --reload               # Start server with auto-reload (development)
  erp-bin start --db mydb --reload     # Start with database and auto-reload
        """
    )

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')

    # Init command parser
    init_parser = subparsers.add_parser('init', help='Initialize database')
    init_parser.add_argument('db_name', help='Database name to initialize')
    init_parser.add_argument('--force', action='store_true',
                           help='Force initialization even if database is already initialized')
    init_parser.add_argument('--demo', action='store_true',
                           help='Install demo data if available')
    init_parser.add_argument('--exit', action='store_true',
                           help='Exit after initialization instead of keeping process running')
    init_parser.add_argument('--no-http', action='store_true',
                           help='Skip HTTP server startup')
    init_parser.add_argument('--quiet', action='store_true',
                           help='Suppress banner output')

    # Start command parser
    start_parser = subparsers.add_parser('start', help='Start HTTP server')
    start_parser.add_argument('--db', type=str, dest='db_name',
                            help='Database name to start with (optional)')
    start_parser.add_argument('--host', type=str,
                            help='Host interface to bind to (overrides config)')
    start_parser.add_argument('--port', type=int,
                            help='Port to bind to (overrides config)')
    start_parser.add_argument('--reload', action='store_true',
                            help='Enable auto-reload for development (watches Python files)')
    start_parser.add_argument('--quiet', action='store_true',
                            help='Suppress banner output')

    # Common arguments
    parser.add_argument('--version', action='version', version='ERP System 1.0.0')

    return parser


def print_system_info(db_name: str = None):
    """Print clean system information instead of banner"""
    from erp.utils.system_info import print_startup_banner
    print_startup_banner(db_name)


async def main():
    """Main function for database initialization"""
    parser = create_parser()
    args = parser.parse_args()

    # Show system info unless quiet mode
    if not args.quiet:
        db_name = getattr(args, 'db_name', None)
        print_system_info(db_name)

    # Initialize basic logging
    try:
        initialize_logging(config.logging_config)
        logger = get_logger(__name__)
        logger.debug("🚀 ERP Database Management Tool starting...")
    except Exception:
        # Fallback to basic logging
        import logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)

    # Handle init and start commands
    if args.command == 'init':
        success = await initialize_database(args.db_name, args.force, args.demo)

        if success:
            if args.exit:
                logger.info("🚪 Exiting after initialization (--exit flag specified)")
                return 0
            elif args.no_http:
                await start_server_without_http(args.db_name)
                return 0
            else:
                # Default behavior: start HTTP server with the initialized database
                await start_http_server(args.db_name)
                return 0
        else:
            return 1

    elif args.command == 'start':
        if args.db_name:
            # Start with specific database
            success = await start_existing_database(args.db_name, args.host, args.port, args.reload)
            return 0 if success else 1
        else:
            # Start without database context
            await start_server_without_database(args.host, args.port, args.reload)
            return 0

    return 1


if __name__ == '__main__':
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        # Use print for immediate user feedback during interruption
        print("\n🛑 Operation cancelled by user")
        sys.exit(130)
    except Exception as e:
        # Use print for critical errors that might prevent logging system from working
        print(f"💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
