"""
System information and health check routes
"""
import os
from pathlib import Path
from fastapi import Request, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse, FileResponse

from ..config import config
from ..database.registry import DatabaseRegistry
from ..utils.responses import APIResponse
from ..templates.manager import get_template_manager
from ..http import systemRoute


@systemRoute("/", methods=["GET"], type="json", auth="none")
async def root():
    """Root endpoint"""
    # If list_db is enabled, redirect to database list regardless of mode
    if config.list_db:
        return RedirectResponse(url="/databases", status_code=302)
    return APIResponse.success({"message": "ERP System API", "version": "1.0.0"})


@systemRoute("/health", methods=["GET"], type="json", auth="none")
async def health_check():
    """Health check endpoint - only checks server status, not database connections"""
    try:
        # Only check server health, no database connections during startup
        return APIResponse.success({
            "status": "healthy",
            "server": "running",
            "message": "ERP server is running"
        })
    except Exception as e:
        return APIResponse.error(f"Health check failed: {str(e)}", 503)


# Removed duplicate /databases route - now handled by erp/routes/database.py
# Only system-level routes and health check are kept here
# Note: /home route is handled by addon controller in addons/base/controllers/home_controller.py








@systemRoute("/static/{filepath:path}", methods=["GET"], type="http", auth="none")
async def serve_static_file(request: Request):
    """
    Serve static files from the static directory

    This route handles all static file requests with the pattern /static/:filepath
    and serves files from the static directory in the project root.
    """
    # Extract filepath from the request path
    filepath = request.path_params.get('filepath', '')

    if not filepath:
        raise HTTPException(status_code=404, detail="File not found")

    # Construct the full file path
    static_dir = Path("static")
    file_path = static_dir / filepath

    # Security check: ensure the file path is within the static directory
    try:
        file_path = file_path.resolve()
        static_dir = static_dir.resolve()

        # Check if the resolved path is within the static directory
        if not str(file_path).startswith(str(static_dir)):
            raise HTTPException(status_code=403, detail="Access denied")

    except (OSError, ValueError):
        raise HTTPException(status_code=400, detail="Invalid file path")

    # Check if file exists and is a file (not a directory)
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")

    if not file_path.is_file():
        raise HTTPException(status_code=404, detail="File not found")

    # Return the file
    return FileResponse(
        path=str(file_path),
        filename=file_path.name,
        media_type=None  # Let FastAPI auto-detect the media type
    )