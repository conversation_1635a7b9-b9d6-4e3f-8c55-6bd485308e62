import logging
from fastapi import Request, HTTPException
from typing import Callable, Any
from ...logging.utils import log_structured
from ..responses import handle_generic_error

logger = logging.getLogger(__name__)

class ErrorHandlingMiddleware:
    """Handles global errors and logs them with context."""

    @staticmethod
    async def process_request(request: Request, call_next: Callable) -> Any:
        """Processes the request and handles any unhandled exceptions."""
        try:
            response = await call_next(request)
            return response
        except HTTPException:
            raise
        except Exception as e:
            log_structured(
                logger, logging.ERROR,
                f"Unhandled error in {request.method} {request.url}: {e}",
                error_type=type(e).__name__,
                method=request.method,
                url=str(request.url),
                user_agent=request.headers.get('user-agent'),
                client_ip=request.client.host if request.client else 'unknown',
                operation="error_handling"
            )

            # Use route-aware error response instead of generic error
            try:
                from ...http.core.error_detection import ErrorResponseFactory
                return ErrorResponseFactory.create_error_response(
                    error=e,
                    request=request,
                    status_code=500
                )
            except Exception as fallback_error:
                logger.error(f"Error in error response creation: {fallback_error}")
                raise handle_generic_error(e)