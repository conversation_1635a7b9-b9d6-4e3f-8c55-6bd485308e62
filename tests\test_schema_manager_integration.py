"""
Integration test for BaseSchemaManager refactoring
Tests the actual model discovery functionality
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from addons.base.schema_manager import BaseSchemaManager
from erp.models.registry import ModelRegistry


class TestSchemaManagerIntegration:
    """Integration tests for the refactored BaseSchemaManager"""

    def test_model_registry_discovers_base_models(self):
        """Test that ModelRegistry can actually discover base models"""
        registry = ModelRegistry('base')
        registry.discover_models()
        
        models = registry.all()
        
        # Verify that models are discovered
        assert len(models) > 0, "No models were discovered"
        
        # Check for expected base models
        expected_models = ['ir.module.module', 'ir.model', 'ir.model.fields']
        discovered_model_names = list(models.keys())
        
        print(f"Discovered models: {discovered_model_names}")
        
        # Verify that at least some expected models are found
        found_models = [model for model in expected_models if model in discovered_model_names]
        assert len(found_models) > 0, f"None of the expected models {expected_models} were found in {discovered_model_names}"

    def test_schema_manager_model_discovery(self):
        """Test that BaseSchemaManager can discover models"""
        schema_manager = BaseSchemaManager()
        
        # Get discovered models
        models = schema_manager.get_discovered_models()
        
        # Verify models are discovered
        assert len(models) > 0, "BaseSchemaManager discovered no models"
        
        # Verify model structure
        for model_name, model_class in models.items():
            assert hasattr(model_class, '_name'), f"Model {model_name} missing _name attribute"
            assert model_class._name == model_name, f"Model name mismatch: {model_class._name} != {model_name}"

    def test_schema_manager_caching(self):
        """Test that BaseSchemaManager caches the ModelRegistry"""
        schema_manager = BaseSchemaManager()
        
        # Get registry twice
        registry1 = schema_manager._get_model_registry()
        registry2 = schema_manager._get_model_registry()
        
        # Should be the same instance
        assert registry1 is registry2, "ModelRegistry should be cached"

    def test_model_discovery_consistency(self):
        """Test that model discovery is consistent across multiple calls"""
        schema_manager = BaseSchemaManager()
        
        # Get models multiple times
        models1 = schema_manager.get_discovered_models()
        models2 = schema_manager.get_discovered_models()
        
        # Should be identical
        assert models1.keys() == models2.keys(), "Model discovery should be consistent"
        
        # Verify same model classes
        for model_name in models1.keys():
            assert models1[model_name] is models2[model_name], f"Model class for {model_name} should be the same instance"

    def test_no_hardcoded_dependencies(self):
        """Test that the refactored code doesn't rely on hardcoded model lists"""
        schema_manager = BaseSchemaManager()
        
        # Verify that the old hardcoded constants are not used in the new methods
        # This is more of a structural test to ensure the refactoring was complete
        
        # The schema manager should still have the constants for constraints/indexes
        assert hasattr(schema_manager, 'BASE_CONSTRAINTS')
        assert hasattr(schema_manager, 'BASE_INDEXES')
        
        # But it should not have the old model mappings being used
        # (They might still exist for backward compatibility but shouldn't be used)
        models = schema_manager.get_discovered_models()
        
        # Verify that discovered models are not empty (proving discovery works)
        assert len(models) > 0, "Model discovery should work without hardcoded mappings"

    def test_model_classes_have_required_attributes(self):
        """Test that discovered model classes have the required attributes for schema generation"""
        schema_manager = BaseSchemaManager()
        models = schema_manager.get_discovered_models()
        
        for model_name, model_class in models.items():
            # Check required attributes for schema generation
            assert hasattr(model_class, '_name'), f"Model {model_name} missing _name"
            assert hasattr(model_class, '_fields'), f"Model {model_name} missing _fields"
            
            # Verify _name matches the key
            assert model_class._name == model_name, f"Model name mismatch for {model_name}"
            
            # Verify _fields is a dictionary
            assert isinstance(model_class._fields, dict), f"Model {model_name} _fields should be a dict"

if __name__ == "__main__":
    # Run a simple test to verify functionality
    test = TestSchemaManagerIntegration()
    
    print("Testing model registry discovery...")
    test.test_model_registry_discovers_base_models()
    print("✓ Model registry discovery works")
    
    print("Testing schema manager model discovery...")
    test.test_schema_manager_model_discovery()
    print("✓ Schema manager model discovery works")
    
    print("Testing model discovery consistency...")
    test.test_model_discovery_consistency()
    print("✓ Model discovery is consistent")
    
    print("Testing model classes have required attributes...")
    test.test_model_classes_have_required_attributes()
    print("✓ Model classes have required attributes")
    
    print("\nAll integration tests passed! ✓")
